using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using AutoMapper;
using EAMS.Domain.Aggregates;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;
    private readonly IMapper _mapper;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger,
        IMapper mapper)
    {
        _accommodationService = accommodationService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all accommodations
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations()
    {
        var accommodations = await _accommodationService.GetAll();

        return Ok(_mapper.Map<IEnumerable<AccommodationDto>>(accommodations));
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(Int64 id)
    {
        var accommodation = await _accommodationService.GetById(id);

        if (accommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", id);
        }

        return Ok(_mapper.Map<AccommodationDto>(accommodation));
    }



    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var createdAccommodation = await _accommodationService.Create(accommodation);
        var response = _mapper.Map<AccommodationDto>(createdAccommodation);

        return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, response);
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(Int64 id, AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var updatedAccommodation = await _accommodationService.Update(accommodation);
        var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(Int64 id)
    {
        var result = await _accommodationService.Delete(id);
        return Ok(result);
    }

    /// <summary>
    /// Search accommodations with pagination and sorting
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<SearchResultDto<AccommodationDto>>> SearchAccommodations([FromQuery] AccommodationSearchRequestDto requestDto)
    {
        try
        {
            if (!requestDto.ValidateQuery())
                return BadRequest(
                    "Invalid query. Pagination.Page and Pagination.PageSize are required fields and must be greater than 0");

            var searchTerm = string.Empty;
            var searchProperty = string.Empty;

            if (!string.IsNullOrEmpty(requestDto.Name))
            {
                searchTerm = requestDto.Name;
                searchProperty = nameof(requestDto.Name);
            }
            else if (!string.IsNullOrEmpty(requestDto.Suburb))
            {
                searchTerm = requestDto.Suburb;
                searchProperty = nameof(requestDto.Suburb);
            }
            else
                searchTerm = requestDto.SearchTerm;

            var (results, totalCount) = await _accommodationService.SearchAccommodationsAsync(
                searchTerm, searchProperty, requestDto.Suburb, requestDto.AccommodationType, requestDto.Density, requestDto.Duration, requestDto.Region, requestDto.Inactive,
                requestDto.Pagination.Page, requestDto.Pagination.PageSize, requestDto.Sorting.OrderBy, requestDto.Sorting.Direction);

            var result = new SearchResultDto<AccommodationDto>(
                _mapper.Map<List<AccommodationDto>>(results), requestDto.Pagination.Page, requestDto.Pagination.PageSize, totalCount,
                requestDto.Sorting.OrderBy, requestDto.Sorting.Direction);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

}
