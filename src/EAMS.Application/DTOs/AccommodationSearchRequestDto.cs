using EAMS.Domain.ValueObjects;

namespace EAMS.Application.DTOs;

public class AccommodationSearchRequestDto: SearchRequestDto
{
    public string? Name { get; set; }
    public string? Suburb { get; set; }
    public AccommodationType? AccommodationType { get; set; }
    public Density? Density { get; set; }
    public Duration? Duration { get; set; }
    public Region? Region { get; set; }
    public bool? Inactive { get; set; }

    public override bool ValidateQuery()
    {
        var pageValidation =  base.ValidateQuery();
        if (!pageValidation) 
            return false;

        if (this.Sorting is null)
        {
            this.Sorting = new SearchSorting
            {
                OrderBy = nameof(this.Name),
                Direction = "asc"
            };
        }

        return true;
    }
}