using EAMS.Domain.Aggregates;
using EAMS.Domain.ValueObjects;
namespace EAMS.Application.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<Accommodation>> GetAll();
    Task<Accommodation?> GetById(Int64 id);
    Task<Accommodation> Create(Accommodation accommodation);
    Task<Accommodation> Update(Accommodation accommodation);
    Task<bool> Delete(Int64 id);
    Task<(List<Accommodation> results, int totalCount)> SearchAccommodationsAsync(
        string? searchTerm, string? searchProperty, string? suburb, AccommodationType? accommodationType, Density? density, Duration? duration, Region? region, bool? inactive,
        int pageNumber, int pageSize, string sortBy, string sortDirection);
}
