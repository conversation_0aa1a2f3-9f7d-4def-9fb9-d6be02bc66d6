using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using System.Linq.Expressions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using EAMS.Domain.Aggregates;
using LinqKit;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;

    public AccommodationService(IAccommodationRepository accommodationRepository)
    {
        _accommodationRepository = accommodationRepository;
    }

    public async Task<IEnumerable<Accommodation>> GetAll()
    {
        return await _accommodationRepository.GetAllAsync();

    }

    public async Task<Accommodation?> GetById(Int64 id)
    {
        return await _accommodationRepository.GetByIdAsync(id);
    }

    public async Task<Accommodation> Create(Accommodation accommodation)
    {
        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _accommodationRepository.AddAsync(accommodation);

        // Return the accommodation with its generated ID
        return accommodation;
    }

    public async Task<Accommodation> Update(Accommodation accommodation)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodation.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodation.Id);
        }

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _accommodationRepository.UpdateAsync(accommodation);

        return accommodation;
    }

    public async Task<bool> Delete(Int64 id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _accommodationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<(List<Accommodation> results, int totalCount)> SearchAccommodationsAsync(
        string? searchTerm, string? searchProperty, string? suburb, AccommodationType? accommodationType, Density? density, Duration? duration, Region? region, bool? inactive,
        int pageNumber, int pageSize, string sortBy, string sortDirection)
    {
        Expression<Func<Accommodation, bool>> predicate = a => a.DiscardedAt == null;

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
           predicate = predicate.And(a => 
                a.Name.Contains(searchTerm) ||
                a.StreetLine1.Contains(searchTerm) ||
                (a.StreetLine2 != null && a.StreetLine2.Contains(searchTerm)) ||
                a.Suburb.Contains(searchTerm) ||
                a.State.Contains(searchTerm) ||
                a.Postcode.Contains(searchTerm) ||
                (a.Phone != null && a.Phone.Contains(searchTerm)) ||
                (a.Email != null && a.Email.Contains(searchTerm)) ||
                (a.Website != null && a.Website.Contains(searchTerm))
           );
        }

        if (!string.IsNullOrWhiteSpace(searchProperty))
        {
            predicate = predicate.And(a => a.Name.Contains(searchProperty));
        }

        if (!string.IsNullOrWhiteSpace(suburb))
        {
            predicate = predicate.And(a => a.Suburb.Contains(suburb));
        }

        if (accommodationType.Id > 0)
        {
            predicate = predicate.And(a => a.AccommodationType.Id == accommodationType.Id);
        }

        if (density.Id > 0)
        {
            predicate = predicate.And(a => a.Density.Id == density.Id);
        }

        if (duration.Id > 0)
        {
            predicate = predicate.And(a => a.Duration.Contains(duration));
        }

        if (region.Id > 0 )
        {
            predicate = predicate.And(a => a.Region.Id == region.Id);
        }

        if (inactive.HasValue)
        {
            predicate = predicate.And(a => a.Inactive == inactive);
        }

        // Fetch queryable from repository
        var query = await _accommodationRepository.GetAllAsync(predicate);

        // Get total count before pagination
        var totalCount = query.Count();

        // Apply sorting
        query = ApplySorting(query.AsQueryable(), sortBy, sortDirection);

        // Apply pagination
        var results = query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return (results, totalCount);
    }

    private IQueryable<Accommodation> ApplySorting(IQueryable<Accommodation> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLower() switch
        {
            "name" => isDescending ? query.OrderByDescending(a => a.Name) : query.OrderBy(a => a.Name),
            "suburb" => isDescending ? query.OrderByDescending(a => a.Suburb) : query.OrderBy(a => a.Suburb),
            "state" => isDescending ? query.OrderByDescending(a => a.State) : query.OrderBy(a => a.State),
            "postcode" => isDescending ? query.OrderByDescending(a => a.Postcode) : query.OrderBy(a => a.Postcode),
            _ => query.OrderBy(a => a.Name) // Default sort
        };
    }
}
